import 'dart:math' as math;
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/product_model.dart';
import 'firebase_service.dart';

class ProductService {
  final FirebaseFirestore _firestore = FirebaseService.instance.firestore;

  // Get all products with pagination
  Future<List<ProductModel>> getProducts({
    int limit = 20,
    DocumentSnapshot? lastDocument,
    String? category,
    String? searchQuery,
  }) async {
    try {
      Query query = _firestore.collection('products')
          .where('isActive', isEqualTo: true)
          .orderBy('createdAt', descending: true);

      if (category != null && category.isNotEmpty) {
        query = query.where('category', isEqualTo: category);
      }

      if (searchQuery != null && searchQuery.isNotEmpty) {
        // For simple text search, we'll use array-contains for tags
        // In production, you might want to use Algolia or similar for better search
        query = query.where('tags', arrayContains: searchQuery.toLowerCase());
      }

      if (lastDocument != null) {
        query = query.startAfterDocument(lastDocument);
      }

      query = query.limit(limit);

      final snapshot = await query.get();
      return snapshot.docs
          .map((doc) => ProductModel.fromJson({
                'id': doc.id,
                ...doc.data() as Map<String, dynamic>,
              }))
          .toList();
    } catch (e) {
      throw Exception('Failed to fetch products: $e');
    }
  }

  // Get featured products
  Future<List<ProductModel>> getFeaturedProducts({int limit = 10}) async {
    try {
      final snapshot = await _firestore
          .collection('products')
          .where('isActive', isEqualTo: true)
          .where('isFeatured', isEqualTo: true)
          .orderBy('averageRating', descending: true)
          .limit(limit)
          .get();

      return snapshot.docs
          .map((doc) => ProductModel.fromJson({
                'id': doc.id,
                ...doc.data(),
              }))
          .toList();
    } catch (e) {
      throw Exception('Failed to fetch featured products: $e');
    }
  }

  // Get product by ID
  Future<ProductModel?> getProductById(String productId) async {
    try {
      final doc = await _firestore.collection('products').doc(productId).get();
      if (doc.exists) {
        return ProductModel.fromJson({
          'id': doc.id,
          ...doc.data() as Map<String, dynamic>,
        });
      }
      return null;
    } catch (e) {
      throw Exception('Failed to fetch product: $e');
    }
  }

  // Get products by owner
  Future<List<ProductModel>> getProductsByOwner(String ownerId) async {
    try {
      final snapshot = await _firestore
          .collection('products')
          .where('ownerId', isEqualTo: ownerId)
          .orderBy('createdAt', descending: true)
          .get();

      return snapshot.docs
          .map((doc) => ProductModel.fromJson({
                'id': doc.id,
                ...doc.data(),
              }))
          .toList();
    } catch (e) {
      throw Exception('Failed to fetch user products: $e');
    }
  }

  // Add new product
  Future<String> addProduct(ProductModel product) async {
    try {
      final docRef = await _firestore.collection('products').add({
        ...product.toJson(),
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      });
      return docRef.id;
    } catch (e) {
      throw Exception('Failed to add product: $e');
    }
  }

  // Update product
  Future<void> updateProduct(String productId, Map<String, dynamic> updates) async {
    try {
      await _firestore.collection('products').doc(productId).update({
        ...updates,
        'updatedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      throw Exception('Failed to update product: $e');
    }
  }

  // Delete product
  Future<void> deleteProduct(String productId) async {
    try {
      await _firestore.collection('products').doc(productId).update({
        'isActive': false,
        'updatedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      throw Exception('Failed to delete product: $e');
    }
  }

  // Search products by location
  Future<List<ProductModel>> searchProductsByLocation({
    required double latitude,
    required double longitude,
    required double radiusKm,
    int limit = 20,
  }) async {
    try {
      // Note: For production, you'd want to use GeoFlutterFire or similar
      // for proper geospatial queries. This is a simplified version.
      final snapshot = await _firestore
          .collection('products')
          .where('isActive', isEqualTo: true)
          .limit(limit * 2) // Get more to filter by distance
          .get();

      final products = snapshot.docs
          .map((doc) => ProductModel.fromJson({
                'id': doc.id,
                ...doc.data(),
              }))
          .where((product) {
            final distance = _calculateDistance(
              latitude,
              longitude,
              product.location.latitude,
              product.location.longitude,
            );
            return distance <= radiusKm;
          })
          .take(limit)
          .toList();

      return products;
    } catch (e) {
      throw Exception('Failed to search products by location: $e');
    }
  }

  // Increment view count
  Future<void> incrementViewCount(String productId) async {
    try {
      await _firestore.collection('products').doc(productId).update({
        'viewCount': FieldValue.increment(1),
      });
    } catch (e) {
      // Don't throw error for view count increment
      print('Failed to increment view count: $e');
    }
  }

  // Add to favorites
  Future<void> addToFavorites(String userId, String productId) async {
    try {
      await _firestore.collection('users').doc(userId).update({
        'favoriteItems': FieldValue.arrayUnion([productId]),
      });

      await _firestore.collection('products').doc(productId).update({
        'favoriteCount': FieldValue.increment(1),
      });
    } catch (e) {
      throw Exception('Failed to add to favorites: $e');
    }
  }

  // Remove from favorites
  Future<void> removeFromFavorites(String userId, String productId) async {
    try {
      await _firestore.collection('users').doc(userId).update({
        'favoriteItems': FieldValue.arrayRemove([productId]),
      });

      await _firestore.collection('products').doc(productId).update({
        'favoriteCount': FieldValue.increment(-1),
      });
    } catch (e) {
      throw Exception('Failed to remove from favorites: $e');
    }
  }

  // Get user's favorite products
  Future<List<ProductModel>> getFavoriteProducts(String userId) async {
    try {
      final userDoc = await _firestore.collection('users').doc(userId).get();
      final favoriteIds = List<String>.from(userDoc.data()?['favoriteItems'] ?? []);

      if (favoriteIds.isEmpty) return [];

      final products = <ProductModel>[];
      // Firestore 'in' queries are limited to 10 items
      for (int i = 0; i < favoriteIds.length; i += 10) {
        final batch = favoriteIds.skip(i).take(10).toList();
        final snapshot = await _firestore
            .collection('products')
            .where(FieldPath.documentId, whereIn: batch)
            .where('isActive', isEqualTo: true)
            .get();

        products.addAll(snapshot.docs.map((doc) => ProductModel.fromJson({
              'id': doc.id,
              ...doc.data(),
            })));
      }

      return products;
    } catch (e) {
      throw Exception('Failed to fetch favorite products: $e');
    }
  }

  // Calculate distance between two points (Haversine formula)
  double _calculateDistance(double lat1, double lon1, double lat2, double lon2) {
    const double earthRadius = 6371; // Earth's radius in kilometers
    final double dLat = _degreesToRadians(lat2 - lat1);
    final double dLon = _degreesToRadians(lon2 - lon1);
    final double a = math.sin(dLat / 2) * math.sin(dLat / 2) +
        math.cos(_degreesToRadians(lat1)) *
            math.cos(_degreesToRadians(lat2)) *
            math.sin(dLon / 2) *
            math.sin(dLon / 2);
    final double c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a));
    return earthRadius * c;
  }

  double _degreesToRadians(double degrees) {
    return degrees * (math.pi / 180);
  }
}

// Riverpod providers
final productServiceProvider = Provider<ProductService>((ref) => ProductService());

final featuredProductsProvider = FutureProvider<List<ProductModel>>((ref) async {
  final productService = ref.read(productServiceProvider);
  return await productService.getFeaturedProducts();
});

final recentProductsProvider = FutureProvider<List<ProductModel>>((ref) async {
  final productService = ref.read(productServiceProvider);
  return await productService.getProducts(limit: 10);
});

final productProvider = FutureProvider.family<ProductModel?, String>((ref, productId) async {
  final productService = ref.read(productServiceProvider);
  return await productService.getProductById(productId);
});
