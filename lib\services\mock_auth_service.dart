import 'package:flutter_riverpod/flutter_riverpod.dart';

class MockUser {
  final String uid;
  final String email;
  final String displayName;

  MockUser({
    required this.uid,
    required this.email,
    required this.displayName,
  });
}

class MockAuthService {
  static MockUser? _currentUser = MockUser(
    uid: 'mock_user_123',
    email: '<EMAIL>',
    displayName: 'Demo User',
  );

  MockUser? get currentUser => _currentUser;

  Stream<MockUser?> get authStateChanges async* {
    yield _currentUser;
  }

  Future<void> signOut() async {
    _currentUser = null;
  }

  Future<void> signIn() async {
    _currentUser = MockUser(
      uid: 'mock_user_123',
      email: '<EMAIL>',
      displayName: 'Demo User',
    );
  }
}

// Riverpod providers
final mockAuthServiceProvider = Provider<MockAuthService>((ref) => MockAuthService());

final mockAuthStateProvider = StreamProvider<MockUser?>((ref) {
  return ref.watch(mockAuthServiceProvider).authStateChanges;
});
