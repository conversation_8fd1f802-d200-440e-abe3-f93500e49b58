import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/category_model.dart';

class MockCategoryService {
  static final List<CategoryModel> _categories = [
    CategoryModel(
      id: 'electronics',
      name: 'Electronics',
      description: 'Cameras, laptops, gaming consoles, and more',
      iconUrl: 'assets/icons/electronics.png',
      sortOrder: 0,
      metadata: const CategoryMetadata(color: '#2196F3'),
    ),
    CategoryModel(
      id: 'tools',
      name: 'Tools & Equipment',
      description: 'Power tools, hand tools, construction equipment',
      iconUrl: 'assets/icons/tools.png',
      sortOrder: 1,
      metadata: const CategoryMetadata(color: '#FF9800'),
    ),
    CategoryModel(
      id: 'sports',
      name: 'Sports & Recreation',
      description: 'Bikes, camping gear, sports equipment',
      iconUrl: 'assets/icons/sports.png',
      sortOrder: 2,
      metadata: const CategoryMetadata(color: '#4CAF50'),
    ),
    CategoryModel(
      id: 'vehicles',
      name: 'Vehicles',
      description: 'Cars, motorcycles, boats, RVs',
      iconUrl: 'assets/icons/vehicles.png',
      sortOrder: 3,
      metadata: const CategoryMetadata(color: '#F44336'),
    ),
    CategoryModel(
      id: 'home',
      name: 'Home & Garden',
      description: 'Furniture, appliances, garden tools',
      iconUrl: 'assets/icons/home.png',
      sortOrder: 4,
      metadata: const CategoryMetadata(color: '#9C27B0'),
    ),
    CategoryModel(
      id: 'fashion',
      name: 'Fashion & Accessories',
      description: 'Designer clothes, jewelry, bags',
      iconUrl: 'assets/icons/fashion.png',
      sortOrder: 5,
      metadata: const CategoryMetadata(color: '#E91E63'),
    ),
    CategoryModel(
      id: 'events',
      name: 'Events & Parties',
      description: 'Party supplies, decorations, equipment',
      iconUrl: 'assets/icons/events.png',
      sortOrder: 6,
      metadata: const CategoryMetadata(color: '#673AB7'),
    ),
    CategoryModel(
      id: 'music',
      name: 'Music & Audio',
      description: 'Instruments, speakers, recording equipment',
      iconUrl: 'assets/icons/music.png',
      sortOrder: 7,
      metadata: const CategoryMetadata(color: '#009688'),
    ),
  ];

  // Get all categories
  Future<List<CategoryModel>> getCategories() async {
    await Future.delayed(const Duration(milliseconds: 300)); // Simulate network delay
    return List.from(_categories);
  }

  // Get main categories (no parent)
  Future<List<CategoryModel>> getMainCategories() async {
    await Future.delayed(const Duration(milliseconds: 300));
    return _categories.where((cat) => cat.parentCategoryId == null).toList();
  }

  // Get subcategories for a parent category
  Future<List<CategoryModel>> getSubCategories(String parentCategoryId) async {
    await Future.delayed(const Duration(milliseconds: 200));
    return _categories.where((cat) => cat.parentCategoryId == parentCategoryId).toList();
  }

  // Get category by ID
  Future<CategoryModel?> getCategoryById(String categoryId) async {
    await Future.delayed(const Duration(milliseconds: 100));
    try {
      return _categories.firstWhere((cat) => cat.id == categoryId);
    } catch (e) {
      return null;
    }
  }
}

// Riverpod providers
final mockCategoryServiceProvider = Provider<MockCategoryService>((ref) => MockCategoryService());

final mockMainCategoriesProvider = FutureProvider<List<CategoryModel>>((ref) async {
  final categoryService = ref.read(mockCategoryServiceProvider);
  return await categoryService.getMainCategories();
});

final mockSubCategoriesProvider = FutureProvider.family<List<CategoryModel>, String>((ref, parentId) async {
  final categoryService = ref.read(mockCategoryServiceProvider);
  return await categoryService.getSubCategories(parentId);
});

final mockCategoryProvider = FutureProvider.family<CategoryModel?, String>((ref, categoryId) async {
  final categoryService = ref.read(mockCategoryServiceProvider);
  return await categoryService.getCategoryById(categoryId);
});
