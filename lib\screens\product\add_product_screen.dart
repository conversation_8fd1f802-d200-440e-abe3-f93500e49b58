import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';
import '../../services/auth_service.dart';
import '../../services/product_service.dart';
import '../../services/category_service.dart';
import '../../services/image_service.dart';
import '../../models/product_model.dart';
import '../../models/category_model.dart';
import '../../widgets/image_picker_widget.dart';

class AddProductScreen extends ConsumerStatefulWidget {
  const AddProductScreen({super.key});

  @override
  ConsumerState<AddProductScreen> createState() => _AddProductScreenState();
}

class _AddProductScreenState extends ConsumerState<AddProductScreen> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _priceController = TextEditingController();
  final _depositController = TextEditingController();
  final _addressController = TextEditingController();
  final _cityController = TextEditingController();
  final _stateController = TextEditingController();
  final _zipController = TextEditingController();

  String? _selectedCategory;
  String? _selectedSubCategory;
  ProductCondition _selectedCondition = ProductCondition.good;
  bool _isLoading = false;
  List<CategoryModel> _categories = [];
  List<CategoryModel> _subCategories = [];
  List<XFile> _selectedImages = [];

  @override
  void initState() {
    super.initState();
    _loadCategories();
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _priceController.dispose();
    _depositController.dispose();
    _addressController.dispose();
    _cityController.dispose();
    _stateController.dispose();
    _zipController.dispose();
    super.dispose();
  }

  void _loadCategories() async {
    try {
      final categoryService = ref.read(categoryServiceProvider);
      final categories = await categoryService.getMainCategories();

      // If no categories exist, initialize them
      if (categories.isEmpty) {
        _initializeCategories();
        return;
      }

      setState(() {
        _categories = categories;
      });
    } catch (e) {
      print('Error loading categories: $e');
      // Fall back to basic categories if Firebase fails
      _useFallbackCategories();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Using basic categories (offline mode)'),
            backgroundColor: Colors.orange,
          ),
        );
      }
    }
  }

  void _initializeCategories() async {
    try {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Setting up categories for the first time...'),
            duration: Duration(seconds: 3),
          ),
        );
      }

      final categoryService = ref.read(categoryServiceProvider);
      await categoryService.initializeDefaultCategories();

      // Reload categories after initialization
      final categories = await categoryService.getMainCategories();
      setState(() {
        _categories = categories;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Categories initialized successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      print('Failed to initialize categories: $e');
      // Fall back to basic categories if Firebase initialization fails
      _useFallbackCategories();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Using basic categories (offline mode)'),
            backgroundColor: Colors.orange,
          ),
        );
      }
    }
  }

  void _useFallbackCategories() {
    // Create simple fallback categories that don't require Firebase
    final fallbackCategories = [
      CategoryModel(
        id: 'electronics',
        name: 'Electronics',
        description: 'Cameras, laptops, gaming consoles, and more',
        iconUrl: 'assets/icons/electronics.png',
        metadata: const CategoryMetadata(color: '#2196F3'),
      ),
      CategoryModel(
        id: 'tools',
        name: 'Tools & Equipment',
        description: 'Power tools, hand tools, construction equipment',
        iconUrl: 'assets/icons/tools.png',
        metadata: const CategoryMetadata(color: '#FF9800'),
      ),
      CategoryModel(
        id: 'sports',
        name: 'Sports & Recreation',
        description: 'Bikes, camping gear, sports equipment',
        iconUrl: 'assets/icons/sports.png',
        metadata: const CategoryMetadata(color: '#4CAF50'),
      ),
      CategoryModel(
        id: 'vehicles',
        name: 'Vehicles',
        description: 'Cars, motorcycles, boats, RVs',
        iconUrl: 'assets/icons/vehicles.png',
        metadata: const CategoryMetadata(color: '#F44336'),
      ),
      CategoryModel(
        id: 'home',
        name: 'Home & Garden',
        description: 'Furniture, appliances, garden tools',
        iconUrl: 'assets/icons/home.png',
        metadata: const CategoryMetadata(color: '#9C27B0'),
      ),
      CategoryModel(
        id: 'fashion',
        name: 'Fashion & Accessories',
        description: 'Designer clothes, jewelry, bags',
        iconUrl: 'assets/icons/fashion.png',
        metadata: const CategoryMetadata(color: '#E91E63'),
      ),
    ];

    setState(() {
      _categories = fallbackCategories;
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Basic categories loaded successfully!'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _loadSubCategories(String categoryId) async {
    try {
      final categoryService = ref.read(categoryServiceProvider);
      final subCategories = await categoryService.getSubCategories(categoryId);
      setState(() {
        _subCategories = subCategories;
        _selectedSubCategory = null;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to load subcategories: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('List Your Item'),
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: () => context.go('/home'),
        ),
        actions: [
          if (_categories.isEmpty)
            PopupMenuButton<String>(
              icon: const Icon(Icons.more_vert),
              onSelected: (value) {
                if (value == 'init') {
                  _initializeCategories();
                } else if (value == 'fallback') {
                  _useFallbackCategories();
                }
              },
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'init',
                  child: Row(
                    children: [
                      Icon(Icons.refresh),
                      SizedBox(width: 8),
                      Text('Initialize Categories'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'fallback',
                  child: Row(
                    children: [
                      Icon(Icons.list),
                      SizedBox(width: 8),
                      Text('Use Basic Categories'),
                    ],
                  ),
                ),
              ],
            ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Title
              Text(
                'Tell us about your item',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 24),

              // Product Title
              TextFormField(
                controller: _titleController,
                decoration: const InputDecoration(
                  labelText: 'Item Title *',
                  hintText: 'e.g., Canon EOS R5 Camera',
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a title';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // Description
              TextFormField(
                controller: _descriptionController,
                maxLines: 4,
                decoration: const InputDecoration(
                  labelText: 'Description *',
                  hintText: 'Describe your item, its condition, and any important details...',
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a description';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // Category Selection
              Row(
                children: [
                  Expanded(
                    child: _categories.isEmpty
                        ? Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              TextFormField(
                                decoration: const InputDecoration(
                                  labelText: 'Category *',
                                  hintText: 'Loading categories...',
                                ),
                                enabled: false,
                              ),
                              const SizedBox(height: 8),
                              Row(
                                children: [
                                  const Icon(Icons.info_outline, size: 16, color: Colors.orange),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: Text(
                                      'Categories are being set up. Please wait or click the refresh button above.',
                                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                        color: Colors.orange.shade700,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          )
                        : DropdownButtonFormField<String>(
                            value: _selectedCategory,
                            decoration: const InputDecoration(
                              labelText: 'Category *',
                            ),
                            items: _categories.map((category) {
                              return DropdownMenuItem(
                                value: category.id,
                                child: Text(category.name),
                              );
                            }).toList(),
                            onChanged: (value) {
                              setState(() {
                                _selectedCategory = value;
                                _selectedSubCategory = null;
                                _subCategories = [];
                              });
                              if (value != null) {
                                _loadSubCategories(value);
                              }
                            },
                            validator: (value) {
                              if (value == null) {
                                return 'Please select a category';
                              }
                              return null;
                            },
                          ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: DropdownButtonFormField<String>(
                      value: _selectedSubCategory,
                      decoration: const InputDecoration(
                        labelText: 'Subcategory',
                      ),
                      items: _subCategories.map((subCategory) {
                        return DropdownMenuItem(
                          value: subCategory.id,
                          child: Text(subCategory.name),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedSubCategory = value;
                        });
                      },
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Condition
              DropdownButtonFormField<ProductCondition>(
                value: _selectedCondition,
                decoration: const InputDecoration(
                  labelText: 'Condition *',
                ),
                items: ProductCondition.values.map((condition) {
                  return DropdownMenuItem(
                    value: condition,
                    child: Text(_getConditionText(condition)),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _selectedCondition = value;
                    });
                  }
                },
              ),
              const SizedBox(height: 16),

              // Pricing
              Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      controller: _priceController,
                      keyboardType: TextInputType.number,
                      decoration: const InputDecoration(
                        labelText: 'Price per Day *',
                        prefixText: '\$',
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter a price';
                        }
                        if (double.tryParse(value) == null) {
                          return 'Please enter a valid number';
                        }
                        return null;
                      },
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: TextFormField(
                      controller: _depositController,
                      keyboardType: TextInputType.number,
                      decoration: const InputDecoration(
                        labelText: 'Security Deposit',
                        prefixText: '\$',
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),

              // Image Upload Section
              ImagePickerWidget(
                selectedImages: _selectedImages,
                onImagesChanged: (images) {
                  setState(() {
                    _selectedImages = images;
                  });
                },
                maxImages: 5,
                title: 'Product Photos',
              ),

              const SizedBox(height: 24),

              // Location Section
              Text(
                'Location',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),

              // Address
              TextFormField(
                controller: _addressController,
                decoration: const InputDecoration(
                  labelText: 'Address *',
                  hintText: '123 Main Street',
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter an address';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // City, State, Zip
              Row(
                children: [
                  Expanded(
                    flex: 2,
                    child: TextFormField(
                      controller: _cityController,
                      decoration: const InputDecoration(
                        labelText: 'City *',
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter a city';
                        }
                        return null;
                      },
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: TextFormField(
                      controller: _stateController,
                      decoration: const InputDecoration(
                        labelText: 'State *',
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter a state';
                        }
                        return null;
                      },
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: TextFormField(
                      controller: _zipController,
                      decoration: const InputDecoration(
                        labelText: 'ZIP *',
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter a ZIP code';
                        }
                        return null;
                      },
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 32),

              // Submit Button
              ElevatedButton(
                onPressed: _isLoading ? null : _handleSubmit,
                child: _isLoading
                    ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Text('List Item'),
              ),
              const SizedBox(height: 8),

              // Debug info
              Consumer(
                builder: (context, ref, child) {
                  final authService = ref.read(authServiceProvider);
                  final user = authService.currentUser;
                  return Text(
                    'Debug: ${user != null ? 'Logged in as ${user.email}' : 'Not logged in'}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey,
                    ),
                  );
                },
              ),
              const SizedBox(height: 16),
            ],
          ),
        ),
      ),
    );
  }

  String _getConditionText(ProductCondition condition) {
    switch (condition) {
      case ProductCondition.newCondition:
        return 'New';
      case ProductCondition.likeNew:
        return 'Like New';
      case ProductCondition.good:
        return 'Good';
      case ProductCondition.fair:
        return 'Fair';
      case ProductCondition.poor:
        return 'Poor';
    }
  }

  void _handleSubmit() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
      });

      try {
        print('Starting product submission...');

        // Get current user directly from auth service instead of stream provider
        final authService = ref.read(authServiceProvider);
        final user = authService.currentUser;

        print('Auth state: ${user != null ? 'User logged in (${user.email})' : 'No user'}');

        if (user == null) {
          throw Exception('You must be logged in to list an item');
        }

        final productService = ref.read(productServiceProvider);

        // Skip image upload for now to test basic functionality
        List<String> imageUrls = [];
        print('Skipping image upload for faster testing...');

        // Create product model
        print('Creating product model...');
        final product = ProductModel(
          id: '', // Will be set by Firestore
          ownerId: user.uid,
          title: _titleController.text.trim(),
          description: _descriptionController.text.trim(),
          category: _selectedCategory!,
          subCategory: _selectedSubCategory ?? '',
          imageUrls: imageUrls,
          pricePerDay: double.parse(_priceController.text),
          securityDeposit: _depositController.text.isNotEmpty
              ? double.parse(_depositController.text)
              : null,
          condition: _selectedCondition,
          availability: const ProductAvailability(isAvailable: true),
          location: ProductLocation(
            address: _addressController.text.trim(),
            city: _cityController.text.trim(),
            state: _stateController.text.trim(),
            zipCode: _zipController.text.trim(),
            country: 'US',
            latitude: 0.0, // TODO: Get from geocoding
            longitude: 0.0, // TODO: Get from geocoding
          ),
          specs: const ProductSpecs(),
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        print('Saving product to database...');
        final productId = await productService.addProduct(product).timeout(
          const Duration(seconds: 30),
          onTimeout: () {
            throw Exception('Request timed out. Please check your internet connection and try again.');
          },
        );
        print('Product saved with ID: $productId');

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Item listed successfully!'),
              backgroundColor: Colors.green,
            ),
          );
          context.pop();
        }
      } catch (e) {
        print('Error during product submission: $e');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to list item: $e'),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 5),
            ),
          );
        }
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }
}
