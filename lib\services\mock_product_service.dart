import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/product_model.dart';

class MockProductService {
  static final List<ProductModel> _products = [
    ProductModel(
      id: '1',
      ownerId: 'user1',
      title: 'Canon EOS R5 Camera',
      description: 'Professional mirrorless camera with 45MP sensor. Perfect for photography and videography.',
      category: 'electronics',
      subCategory: 'cameras',
      imageUrls: [
        'https://images.unsplash.com/photo-1606983340126-99ab4feaa64a?w=400',
        'https://images.unsplash.com/photo-1502920917128-1aa500764cbd?w=400'
      ],
      pricePerDay: 75.0,
      securityDeposit: 500.0,
      condition: ProductCondition.likeNew,
      availability: const ProductAvailability(isAvailable: true),
      location: const ProductLocation(
        address: '123 Main St',
        city: 'San Francisco',
        state: 'CA',
        zipCode: '94102',
        country: 'US',
        latitude: 37.7749,
        longitude: -122.4194,
      ),
      specs: const ProductSpecs(),
      createdAt: DateTime.now().subtract(const Duration(days: 2)),
      updatedAt: DateTime.now().subtract(const Duration(days: 2)),
      averageRating: 4.8,
      reviewCount: 12,
    ),
    ProductModel(
      id: '2',
      ownerId: 'user2',
      title: 'MacBook Pro 16" M3',
      description: 'Latest MacBook Pro with M3 chip. Great for video editing and development work.',
      category: 'electronics',
      subCategory: 'laptops',
      imageUrls: [
        'https://images.unsplash.com/photo-1541807084-5c52b6b3adef?w=400',
        'https://images.unsplash.com/photo-1496181133206-80ce9b88a853?w=400'
      ],
      pricePerDay: 50.0,
      securityDeposit: 1000.0,
      condition: ProductCondition.good,
      availability: const ProductAvailability(isAvailable: true),
      location: const ProductLocation(
        address: '456 Oak Ave',
        city: 'Los Angeles',
        state: 'CA',
        zipCode: '90210',
        country: 'US',
        latitude: 34.0522,
        longitude: -118.2437,
      ),
      specs: const ProductSpecs(),
      createdAt: DateTime.now().subtract(const Duration(days: 1)),
      updatedAt: DateTime.now().subtract(const Duration(days: 1)),
      averageRating: 4.5,
      reviewCount: 8,
    ),
    ProductModel(
      id: '3',
      ownerId: 'user3',
      title: 'DJI Mavic Air 2 Drone',
      description: 'Professional drone with 4K camera. Perfect for aerial photography and videography.',
      category: 'electronics',
      subCategory: 'drones',
      imageUrls: [
        'https://images.unsplash.com/photo-1473968512647-3e447244af8f?w=400',
        'https://images.unsplash.com/photo-1508614589041-895b88991e3e?w=400'
      ],
      pricePerDay: 40.0,
      securityDeposit: 300.0,
      condition: ProductCondition.likeNew,
      availability: const ProductAvailability(isAvailable: true),
      location: const ProductLocation(
        address: '789 Pine St',
        city: 'Seattle',
        state: 'WA',
        zipCode: '98101',
        country: 'US',
        latitude: 47.6062,
        longitude: -122.3321,
      ),
      specs: const ProductSpecs(),
      createdAt: DateTime.now().subtract(const Duration(hours: 12)),
      updatedAt: DateTime.now().subtract(const Duration(hours: 12)),
      averageRating: 4.9,
      reviewCount: 15,
    ),
    // Add more products in different categories
    ProductModel(
      id: '4',
      ownerId: 'user4',
      title: 'DeWalt Power Drill Set',
      description: 'Professional cordless drill with multiple bits. Perfect for construction and DIY projects.',
      category: 'tools',
      subCategory: 'power-tools',
      imageUrls: [
        'https://images.unsplash.com/photo-1504148455328-c376907d081c?w=400',
        'https://images.unsplash.com/photo-1581244277943-fe4a9c777189?w=400'
      ],
      pricePerDay: 25.0,
      securityDeposit: 100.0,
      condition: ProductCondition.good,
      availability: const ProductAvailability(isAvailable: true),
      location: const ProductLocation(
        address: '321 Tool St',
        city: 'Denver',
        state: 'CO',
        zipCode: '80202',
        country: 'US',
        latitude: 39.7392,
        longitude: -104.9903,
      ),
      specs: const ProductSpecs(),
      createdAt: DateTime.now().subtract(const Duration(hours: 8)),
      updatedAt: DateTime.now().subtract(const Duration(hours: 8)),
      averageRating: 4.3,
      reviewCount: 7,
    ),
    ProductModel(
      id: '5',
      ownerId: 'user5',
      title: 'Mountain Bike - Trek',
      description: 'High-quality mountain bike perfect for trails and outdoor adventures.',
      category: 'sports',
      subCategory: 'bicycles',
      imageUrls: [
        'https://images.unsplash.com/photo-1558618047-3c8c76ca7d13?w=400',
        'https://images.unsplash.com/photo-1571068316344-75bc76f77890?w=400'
      ],
      pricePerDay: 35.0,
      securityDeposit: 200.0,
      condition: ProductCondition.likeNew,
      availability: const ProductAvailability(isAvailable: true),
      location: const ProductLocation(
        address: '654 Bike Lane',
        city: 'Portland',
        state: 'OR',
        zipCode: '97201',
        country: 'US',
        latitude: 45.5152,
        longitude: -122.6784,
      ),
      specs: const ProductSpecs(),
      createdAt: DateTime.now().subtract(const Duration(hours: 6)),
      updatedAt: DateTime.now().subtract(const Duration(hours: 6)),
      averageRating: 4.7,
      reviewCount: 11,
    ),
    ProductModel(
      id: '6',
      ownerId: 'user6',
      title: 'Tesla Model 3',
      description: 'Electric vehicle perfect for city driving and road trips. Clean and efficient.',
      category: 'vehicles',
      subCategory: 'cars',
      imageUrls: [
        'https://images.unsplash.com/photo-1560958089-b8a1929cea89?w=400',
        'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400'
      ],
      pricePerDay: 120.0,
      securityDeposit: 1000.0,
      condition: ProductCondition.likeNew,
      availability: const ProductAvailability(isAvailable: true),
      location: const ProductLocation(
        address: '987 Electric Ave',
        city: 'Austin',
        state: 'TX',
        zipCode: '73301',
        country: 'US',
        latitude: 30.2672,
        longitude: -97.7431,
      ),
      specs: const ProductSpecs(),
      createdAt: DateTime.now().subtract(const Duration(hours: 4)),
      updatedAt: DateTime.now().subtract(const Duration(hours: 4)),
      averageRating: 4.9,
      reviewCount: 23,
    ),
  ];

  // Get all products
  Future<List<ProductModel>> getProducts({
    int limit = 20,
    String? category,
    String? searchQuery,
  }) async {
    await Future.delayed(const Duration(milliseconds: 500)); // Simulate network delay
    
    var products = List<ProductModel>.from(_products);
    
    if (category != null && category.isNotEmpty) {
      products = products.where((p) => p.category == category).toList();
    }
    
    if (searchQuery != null && searchQuery.isNotEmpty) {
      products = products.where((p) => 
        p.title.toLowerCase().contains(searchQuery.toLowerCase()) ||
        p.description.toLowerCase().contains(searchQuery.toLowerCase())
      ).toList();
    }
    
    products.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    return products.take(limit).toList();
  }

  // Get featured products
  Future<List<ProductModel>> getFeaturedProducts({int limit = 10}) async {
    await Future.delayed(const Duration(milliseconds: 300));
    return _products.where((p) => p.averageRating >= 4.5).take(limit).toList();
  }

  // Get product by ID
  Future<ProductModel?> getProductById(String productId) async {
    await Future.delayed(const Duration(milliseconds: 200));
    try {
      return _products.firstWhere((p) => p.id == productId);
    } catch (e) {
      return null;
    }
  }

  // Add new product
  Future<String> addProduct(ProductModel product) async {
    await Future.delayed(const Duration(milliseconds: 800)); // Simulate network delay
    
    final newId = DateTime.now().millisecondsSinceEpoch.toString();
    final newProduct = product.copyWith(
      id: newId,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
    
    _products.insert(0, newProduct); // Add to beginning for recent products
    return newId;
  }

  // Get products by owner
  Future<List<ProductModel>> getProductsByOwner(String ownerId) async {
    await Future.delayed(const Duration(milliseconds: 300));
    return _products.where((p) => p.ownerId == ownerId).toList();
  }
}

// Riverpod providers
final mockProductServiceProvider = Provider<MockProductService>((ref) => MockProductService());

final mockRecentProductsProvider = FutureProvider<List<ProductModel>>((ref) async {
  final productService = ref.read(mockProductServiceProvider);
  return await productService.getProducts(limit: 10);
});

final mockFeaturedProductsProvider = FutureProvider<List<ProductModel>>((ref) async {
  final productService = ref.read(mockProductServiceProvider);
  return await productService.getFeaturedProducts();
});

final mockProductProvider = FutureProvider.family<ProductModel?, String>((ref, productId) async {
  final productService = ref.read(mockProductServiceProvider);
  return await productService.getProductById(productId);
});

final mockProductsByCategoryProvider = FutureProvider.family<List<ProductModel>, String>((ref, category) async {
  final productService = ref.read(mockProductServiceProvider);
  return await productService.getProducts(category: category, limit: 20);
});
